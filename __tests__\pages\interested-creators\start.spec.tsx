import React from "react";
import { useRouter } from "next/router";
import { render, screen, waitFor } from "@testing-library/react";
import { useDependency } from "@src/context/DependencyContext";
import { ApplicationStartPageLabels } from "@src/contentManagement/ApplicationStartPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import Start from "pages/interested-creators/start";

jest.mock("../../../src/context/DependencyContext");

describe("Interested Creator Start Page", () => {
  const router = { locale: "en-us", push: jest.fn() };
  const analytics = {
    checkedApplicationStatus: jest.fn()
  };
  const applicationStartProps = {
    pageLabels: {
      applicationStartPageLabels: {},
      informationLabels: {
        interestedCreatorTitle: "Start Page"
      },
      commonPageLabels: {}
    } as ApplicationStartPageLabels & CommonPageLabels & InformationPageLabels
  };

  beforeEach(() => {
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { APPLICATIONS_MFE_BASE_URL: "http://localhost:3003" },
      analytics: analytics
    });
  });

  it("shows tab title as 'Start your submission'", async () => {
    render(<Start {...applicationStartProps} />);

    await waitFor(() => {
      expect(document.title).toMatch(/Start Page/);
    });
  });

  it("shows remote application start component", async () => {
    render(<Start {...applicationStartProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
