import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import { anInitialInterestedCreator } from "__tests__/factories/initialInterestedCreators/InitialInterestedCreator";
import { useDependency } from "@src/context/DependencyContext";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import BrowserAnalytics, { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import InterestedCreator from "@src/interestedCreators/InterestedCreator";
import { NoAccountLabels } from "@src/contentManagement/NoAccountPageMapper";
import NoAccount, { NoAccountType } from "pages/interested-creators/no-account";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { ApplicationStartPageLabels } from "@src/contentManagement/ApplicationStartPageMapper";

jest.mock("../../../src/context/DependencyContext");

describe("InterestedCreatorsApplicationRejected", () => {
  const locale = "en-us";
  const initialInterestedCreator = {
    nucleusId: Random.nucleusId(),
    originEmail: Random.email(),
    dateOfBirth: LocalizedDate.epochMinusMonths(240).toString(),
    defaultGamerTag: Random.email(),
    analyticsId: Random.uuid()
  };
  const noAccountProps = {
    locale,
    interestedCreator: anInitialInterestedCreator() as InterestedCreator,
    analytics: {} as unknown as BrowserAnalytics,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    pageLabels: {
      noAccountLabels: {
        pageTitle: "No Account Found",
        title: "Account Not Found",
        description: "We couldn't find your account"
      },
      commonPageLabels: {
        creatorNetwork: "Creator Network",
        close: "Close"
      },
      applicationStartPageLabels: {
        title: "Get Started"
      }
    } as unknown as NoAccountLabels & CommonPageLabels & ApplicationStartPageLabels
  } as NoAccountType;


  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale
    }));
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { APPLICATIONS_MFE_BASE_URL: "http://localhost:3003" },
      analytics: {}
    });
  });

  it("shows remote no account component", async () => {
    render(<NoAccount {...noAccountProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
