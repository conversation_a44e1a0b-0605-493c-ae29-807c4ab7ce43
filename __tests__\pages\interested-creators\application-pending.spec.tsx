import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import { anInterestedCreatorApplication } from "__tests__/factories/interestedCreators/InterestedCreatorApplication";
import ApplicationPending from "pages/interested-creators/application-pending";
import InterestedCreatorApplicationStatus from "@src/interestedCreators/InterestedCreatorApplicationStatus";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { ApplicationPendingPageLabels } from "@src/contentManagement/ApplicationPendingPageMapper";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../../src/context/DependencyContext");

describe("InterestedCreatorsApplicationPending", () => {
  const locale = "en-us";
  const analytics = { checkedApplicationStatus: jest.fn() };
  const pageLabels = { applicationPendingLabels: {}, 
  commonPageLabels: {
    creatorNetwork: "Creator Network",
    close: "Close"
  }
} as unknown as ApplicationPendingPageLabels & CommonPageLabels;

  const applicationPendingProps = {
    locale,
    application: anInterestedCreatorApplication() as InterestedCreatorApplicationStatus,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    RE_APPLY_THRESHOLD_IN_DAYS: 0,
    pageLabels
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale
    }));
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { FLAG_INTERESTED_CREATOR_CAN_APPLY: false },
      analytics: analytics
    });
  });

  it("shows remote application pending component", async () => {
    render(<ApplicationPending {...applicationPendingProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
