import "reflect-metadata";
import { screen, waitFor, within } from "@testing-library/react";
import FranchisesYouPlay from "./../../pages/franchises-you-play";
import { aCreatorWithPayableStatus } from "../factories/creators/CreatorWithPayableStatus";
import { aPrimaryFranchise, aSecondaryFranchise } from "../factories/franchises/PreferredFranchise";
import userEvent from "@testing-library/user-event";
import { renderPage } from "../helpers/page";
import { useRouter } from "next/router";
import { mockMatchMedia } from "../helpers/window";
import { useAppContext } from "../../src/context";
import { renderWithToast, triggerAnimationEnd } from "../helpers/toast";
import CreatorsService from "../../src/api/services/CreatorsService";
import {
  communicationPreferences,
  connectAccounts,
  creatorType,
  franchisesYouPlay,
  information,
  termsAndConditions
} from "@eait-playerexp-cn/core-ui-kit";
import { onToastClose } from "../../utils";
import { useDependency } from "@src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../src/context/DependencyContext");
jest.mock("../../src/context", () => ({
  ...jest.requireActual("../../src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../utils", () => ({
  ...jest.requireActual("../../utils"),
  onToastClose: jest.fn()
}));
jest.mock("../../src/api/services/CreatorsService");

describe("FranchisesYouPlay", () => {
  const router = { locale: "en-us", pathname: "/franchises-you-play", push: jest.fn() };
  const analytics = {};
  const franchisesYouPlayProps = {
    analytics
  };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/onboarding/information",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/franchises-you-play",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/creator-type",
      isCompleted: false
    },
    {
      icon: connectAccounts,
      title: "Connect Accounts",
      href: "/connect-accounts",
      isCompleted: false
    },
    {
      icon: communicationPreferences,
      title: "Communication Preferences",
      href: "/communication-preferences",
      isCompleted: false
    },
    {
      icon: termsAndConditions,
      title: "Terms And Conditions",
      href: "/terms-and-conditions",
      isCompleted: false
    }
  ];
  mockMatchMedia();
  const mockUseDetectScreen = jest.fn();
  const errorHandler = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    const franchises = [
      aPrimaryFranchise({ label: "Apex Legends" }),
      aSecondaryFranchise({ label: "Need for Speed" }),
      aSecondaryFranchise({ label: "Madden NFL" })
    ];
    const creator = aCreatorWithPayableStatus({ preferredFranchises: franchises });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });
    useDependency.mockReturnValue({
      errorHandler,
      metadataClient: {},
      creatorsClient: {},
      configuration: {
        PROGRAM_CODE: "creator_network",
        FLAG_PER_PROGRAM_PROFILE: false,
        DEFAULT_AVATAR_IMAGE: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png",
        SUPPORTED_LOCALES: ["en-us", "en-gb"]
      }
    });
    MetadataService.mockReturnValue({
      getFranchises: jest.fn().mockResolvedValue(franchises)
    });
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps
      }
    });
  });

  it("Add/Update creator franchise preferences", async () => {
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} />);

    expect(await screen.findByRole("heading", { name: /franchises-you-play:title/i })).toBeInTheDocument();
  });

  it("shows modal with logout option", async () => {
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /closeHeader/i }));

    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /no/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("logs 'Canceled Onboarding Flow' event when clicking on 'Yes' button in the cancel registration modal", async () => {
    const analytics = { canceledOnboardingFlow: jest.fn() };
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} analytics={analytics} />);
    await userEvent.click(await screen.findByRole("button", { name: /closeHeader/i }));

    await userEvent.click(await screen.findByRole("button", { name: /yes/i }));

    await waitFor(() => {
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledTimes(1);
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledWith({
        locale: "en-us",
        page: "/franchises-you-play"
      });
      expect(router.push).toHaveBeenCalledWith("/api/logout");
    });
  });

  xit("logs 'Confirmed Franchise' event when clicking on 'Next' button in the cancel registration modal", async () => {
    const analytics = { confirmedFranchise: jest.fn() };
    CreatorsService.update.mockImplementation(() => Promise.resolve());

    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} analytics={analytics} />);

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => {
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
      expect(analytics.confirmedFranchise).toHaveBeenCalledTimes(1);
      // TODO: Add missing `toHaveBeenCalledWith`, skipping now since useEffect is being called too many times
      expect(router.push).toHaveBeenCalledWith("/creator-type");
    });
  });

  it("show confirmation modal when user click on 'Back' button with form has unsaved changes", async () => {
    const analytics = { confirmedFranchise: jest.fn() };
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} analytics={analytics} />);
    const primaryFranchise = await screen.findByLabelText(/franchises-you-play:labels.primaryFranchise/i);
    // Click the primary franchise search box
    await userEvent.click(primaryFranchise);
    // Select the primary franchise
    await userEvent.click(await screen.findByText("Apex Legends"));
    expect(primaryFranchise).toHaveValue("Apex Legends");
    const secondaryFranchise = await screen.findByLabelText("Madden NFL");
    await userEvent.click(secondaryFranchise);
    expect(secondaryFranchise).toBeChecked();

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    // There are two cancel buttons in this page used within to find the one in the modal
    const { findByRole, findByText } = within(await screen.findByRole("dialog"));
    expect(await findByRole("heading", { name: /breadcrumb:modalTitle/i })).toBeInTheDocument();
    expect(await findByText(/breadcrumb:modalMessage/i)).toBeInTheDocument();
    expect(await findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /cancel/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /discard/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /save/i })).toBeInTheDocument();
    expect(router.push).not.toBeCalled();
    expect(analytics.confirmedFranchise).not.toBeCalled();
  });

  it("closes the confirmation modal on discard", async () => {
    const analytics = { confirmedFranchise: jest.fn() };
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} analytics={analytics} />);
    // Click the primary franchise search box
    await userEvent.click(await screen.findByLabelText(/franchises-you-play:labels.primaryFranchise/i));
    // Select the primary franchise
    await userEvent.click(await screen.findByText("Apex Legends"));
    const secondaryFranchise = await screen.findByLabelText("Madden NFL");
    await userEvent.click(secondaryFranchise);
    expect(secondaryFranchise).toBeChecked();
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /discard/i }));

    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();
    expect(router.push).toHaveBeenCalledWith("/onboarding/information");
  });

  it("saves the form from confirmation modal and navigates back", async () => {
    const analytics = { confirmedFranchise: jest.fn() };
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} analytics={analytics} />);
    // Click the primary franchise search box
    await userEvent.click(await screen.findByLabelText(/franchises-you-play:labels.primaryFranchise/i));
    // Select the primary franchise
    await userEvent.click(await screen.findByText("Apex Legends"));
    const secondaryFranchise = await screen.findByLabelText("Madden NFL");
    await userEvent.click(secondaryFranchise);
    expect(secondaryFranchise).toBeChecked();
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    CreatorsService.update.mockImplementation(() => Promise.resolve());

    const { findByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /^save$/i }));

    await waitFor(() => {
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    expect(router.push).toHaveBeenCalledWith("/onboarding/information");
  });

  it("closes the confirmation modal", async () => {
    const analytics = { confirmedFranchise: jest.fn() };
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} analytics={analytics} />);
    // Click the primary franchise search box
    await userEvent.click(await screen.findByLabelText(/franchises-you-play:labels.primaryFranchise/i));
    // Select the primary franchise
    await userEvent.click(await screen.findByText("Apex Legends"));
    const secondaryFranchise = await screen.findByLabelText("Madden NFL");
    await userEvent.click(secondaryFranchise);
    expect(secondaryFranchise).toBeChecked();
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /Close$/i }));

    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();
  });

  it("navigates to 'information' page when user click on 'Back' button", async () => {
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        userNavigated: true,
        onboardingSteps: steps
      }
    });
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/onboarding/information"));
  });

  it("detects viewport size and shows page back button for mobile", async () => {
    mockUseDetectScreen.mockImplementation((width) => width === 1279); // size is smaller than desktop
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} />);

    expect(await screen.findByRole("button", { name: /Back/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /Back/i })).toHaveClass("display-back-bt");
  });

  it("saves the form from confirmation modal and navigates with stepper links", async () => {
    const analytics = { confirmedFranchise: jest.fn() };
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} analytics={analytics} />);
    // Click the primary franchise search box
    await userEvent.click(await screen.findByLabelText(/franchises-you-play:labels.primaryFranchise/i));
    // Select the primary franchise
    await userEvent.click(await screen.findByText("Apex Legends"));
    const secondaryFranchise = await screen.findByLabelText("Madden NFL");
    await userEvent.click(secondaryFranchise);
    expect(secondaryFranchise).toBeChecked();
    await userEvent.click(await screen.findByRole("button", { name: /Information/i }));
    CreatorsService.update.mockImplementation(() => Promise.resolve());

    const { findByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /^save$/i }));

    await waitFor(() => {
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    expect(router.push).toHaveBeenCalledWith("/onboarding/information");
  });

  it("navigates to 'creator-type' page when 'Next' is clicked after closing confirmation popup", async () => {
    const analytics = { confirmedFranchise: jest.fn() };
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} analytics={analytics} />);
    // Click the primary franchise search box
    await userEvent.click(await screen.findByLabelText(/franchises-you-play:labels.primaryFranchise/i));
    // Select the primary franchise
    await userEvent.click(await screen.findByText("Apex Legends"));
    const secondaryFranchise = await screen.findByLabelText("Madden NFL");
    await userEvent.click(secondaryFranchise);
    expect(secondaryFranchise).toBeChecked();
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));
    await userEvent.click(await findByRole("button", { name: /cancel/i }));
    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/creator-type"));
    expect(CreatorsService.update).toHaveBeenCalledTimes(1);
  });

  it("navigates to 'Information' page when user click on stepper links without updating any form fields", async () => {
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        userNavigated: true,
        onboardingSteps: steps
      }
    });
    renderPage(<FranchisesYouPlay {...franchisesYouPlayProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /Information/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/onboarding/information"));
  });

  it("closes error toast message on clicking the close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const dispatch = jest.fn();
    const errorMessage = "ERROR";
    useAppContext.mockReturnValue({
      dispatch,
      state: { isError: errorMessage, userNavigated: true, onboardingSteps: steps }
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    const { unmount } = renderWithToast(<FranchisesYouPlay {...franchisesYouPlayProps} />);
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("unhandledError");
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();

    await user.click(screen.getByRole("button", { name: "close" }));

    triggerAnimationEnd(screen.getByText("unhandledError"));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(errorMessage, dispatch);
    });
    unmount();
    jest.useRealTimers();
  });
});
