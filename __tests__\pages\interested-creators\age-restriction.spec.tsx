import { AgeRestrictionPageLabels } from "@src/contentManagement/AgeRestrictionPageMapper";
import AgeRestriction from "pages/interested-creators/age-restriction";
import { render, screen } from "@testing-library/react";
import { useRouter } from "next/router";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

describe("AgeRestriction", () => {
  const router = {
    locale: "en-us",
    push: jest.fn().mockResolvedValue(true)
  };
  const ageRestrictionProps = {
    pageLabels: {
      ageRestrictionLabels: {},
      commonPageLabels: {
        creatorNetwork: "Creator Network",
        close: "Close"
      }
    } as unknown as AgeRestrictionPageLabels & CommonPageLabels,

    ageRestrictionBannerImage: "./img/Players-comp.png",
    onClose: () => {}
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
  });

  it("shows remote age restriction component", async () => {
    render(<AgeRestriction {...ageRestrictionProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
