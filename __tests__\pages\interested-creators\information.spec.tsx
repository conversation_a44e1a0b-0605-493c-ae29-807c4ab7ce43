import "reflect-metadata";
import React from "react";
import { screen } from "@testing-library/react";
import { mockMatchMedia } from "../../helpers/window";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";
import { renderPage } from "__tests__/helpers/page";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import BrowserAnalytics, { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { AddContentPageLabels } from "@src/contentManagement/AddContentPageMapper";
import { CommunicationPreferencesPageLabels } from "@src/contentManagement/CommunicationPreferencesPageMapper";
import { ConnectAccountsPageLabels } from "@src/contentManagement/ConnectAccountsPageMapper";
import Information from "pages/interested-creators/information";

jest.mock("../../../src/context/index", () => ({
  ...(jest.requireActual("../../../src/context/index") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../src/context/DependencyContext");
jest.mock("next/router");

// Mock the dynamic component with props tracking
const mockInformationComponent = jest.fn();
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = (props: Record<string, unknown>) => {
    mockInformationComponent(props);
    // Don't spread all props to avoid React DOM warnings about unknown props
    return <div data-testid="information-component" />;
  };
  DynamicComponent.displayName = "Information";
  return DynamicComponent;
});

describe("InterestedCreators", () => {
  const mockPush = jest.fn();
  const mockDispatch = jest.fn();
  const mockCancelledCreatorApplication = jest.fn();
  const router = { locale: "en-us", push: mockPush };

  const initialInterestedCreator = {
    nucleusId: 12345,
    originEmail: Random.email(),
    dateOfBirth: LocalizedDate.epochMinusMonths(240).toString(),
    defaultGamerTag: Random.email(),
    analyticsId: Random.uuid()
  };

  const mockAnalytics = {
    cancelledCreatorApplication: mockCancelledCreatorApplication
  } as unknown as BrowserAnalytics;

  const baseInterestedCreatorsProps = {
    interestedCreator: initialInterestedCreator,
    analytics: mockAnalytics,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    pageLabels: {
      informationLabels: {
        messages: {
          email: "Email"
        },
        informationPageTitle: "Information Page",
        interestedCreatorTitle: "Creator Information"
      },
      addContentPageLabels: {},
      breadcrumbPageLabels: {},
      communicationPreferencesPageLabels: {
        messages: {},
        labels: {}
      },
      commonPageLabels: {
        back: "Back",
        creatorNetwork: "Creator Network",
        close: "Close",
        information: "Information",
        franchises: "Franchises",
        creatorType: "Creator Type",
        unhandledError: "An error occurred"
      },
      connectAccountsLabels: {
        title: "Connect Accounts",
        description: "Connect your social media accounts"
      }
    } as InformationPageLabels &
      CommonPageLabels &
      BreadcrumbPageLabels &
      AddContentPageLabels &
      CommunicationPreferencesPageLabels &
      ConnectAccountsPageLabels,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    pages: [],
    FLAG_COUNTRIES_BY_TYPE: false
  };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];

  mockMatchMedia();

  beforeEach(() => {
    jest.clearAllMocks();
    mockInformationComponent.mockClear();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: null,
        sessionUser: null,
        isLoading: false
      }
    });
    (useDependency as jest.Mock).mockReturnValue({
      applicationsClient: {},
      errorHandler: jest.fn(),
      metadataClient: {},
      configuration: {
        APPLICATIONS_MFE_BASE_URL: "http://localhost:3003",
        BASE_PATH: "/support-a-creator",
        PROGRAM_CODE: "creator_network",
        SUPPORTED_LOCALES: ["en-us", "en-gb"],
        FLAG_PER_PROGRAM_PROFILE: false
      }
    });
  });

  it("shows information component", async () => {
    renderPage(<Information {...baseInterestedCreatorsProps} />);

    expect(await screen.findByTestId("information-component")).toBeInTheDocument();
  });

  it("passes correct interested creator to Information component", async () => {
    renderPage(<Information {...baseInterestedCreatorsProps} />);

    await screen.findByTestId("information-component");

    expect(mockInformationComponent).toHaveBeenCalledWith(
      expect.objectContaining({
        interestedCreator: initialInterestedCreator
      })
    );
  });

  it("passes correct analytics to Information component", async () => {
    renderPage(<Information {...baseInterestedCreatorsProps} />);

    await screen.findByTestId("information-component");

    expect(mockInformationComponent).toHaveBeenCalledWith(
      expect.objectContaining({
        analytics: mockAnalytics
      })
    );
  });

  it("renders with exception code and shows error page", async () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: 500,
        sessionUser: { id: "test-user" },
        isLoading: false
      }
    });

    renderPage(<Information {...baseInterestedCreatorsProps} />);

    expect(screen.queryByTestId("information-component")).not.toBeInTheDocument();
  });

  it("displays loading state when isLoading is true", async () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: null,
        sessionUser: null,
        isLoading: true
      }
    });

    renderPage(<Information {...baseInterestedCreatorsProps} />);

    expect(await screen.findByTestId("information-component")).toBeInTheDocument();
  });

  it("renders with different page labels", async () => {
    const propsWithDifferentLabels = {
      ...baseInterestedCreatorsProps,
      pageLabels: {
        ...baseInterestedCreatorsProps.pageLabels,
        informationLabels: {
          ...baseInterestedCreatorsProps.pageLabels.informationLabels,
          informationPageTitle: "Custom Information Title"
        }
      }
    };

    renderPage(<Information {...propsWithDifferentLabels} />);

    expect(await screen.findByTestId("information-component")).toBeInTheDocument();
  });

  it("handles confirmation modal state changes", async () => {
    renderPage(<Information {...baseInterestedCreatorsProps} />);

    await screen.findByTestId("information-component");

    expect(screen.getByTestId("information-component")).toBeInTheDocument();
  });

  it("renders with INTERESTED_CREATOR_REAPPLY_PERIOD enabled", async () => {
    const propsWithReapplyEnabled = {
      ...baseInterestedCreatorsProps,
      INTERESTED_CREATOR_REAPPLY_PERIOD: true
    };

    renderPage(<Information {...propsWithReapplyEnabled} />);

    expect(await screen.findByTestId("information-component")).toBeInTheDocument();
  });

  it("renders with FLAG_COUNTRIES_BY_TYPE enabled", async () => {
    const propsWithCountriesByType = {
      ...baseInterestedCreatorsProps,
      FLAG_COUNTRIES_BY_TYPE: true
    };

    renderPage(<Information {...propsWithCountriesByType} />);

    expect(await screen.findByTestId("information-component")).toBeInTheDocument();
  });

  it("passes correct connect accounts configuration", async () => {
    renderPage(<Information {...baseInterestedCreatorsProps} />);

    const dynamicComponent = await screen.findByTestId("information-component");

    expect(dynamicComponent).toBeInTheDocument();
    expect(dynamicComponent).toHaveAttribute("data-testid", "information-component");
  });
});
