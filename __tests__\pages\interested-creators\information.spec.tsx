import "reflect-metadata";
import React from "react";
import { screen } from "@testing-library/react";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";
import { renderPage } from "__tests__/helpers/page";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import Information from "pages/interested-creators/information";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { CommunicationPreferencesPageLabels } from "@src/contentManagement/CommunicationPreferencesPageMapper";
import { ConnectAccountsPageLabels } from "@src/contentManagement/ConnectAccountsPageMapper";
import { AddContentPageLabels } from "@src/contentManagement/AddContentPageMapper";
import { mockMatchMedia } from "../../helpers/window";

jest.mock("../../../src/context/index", () => ({
  ...(jest.requireActual("../../../src/context/index") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../src/context/DependencyContext");

describe("InterestedCreators", () => {
  const router = { locale: "en-us", push: jest.fn() };
  const initialInterestedCreator = {
    nucleusId: Random.nucleusId(),
    originEmail: Random.email(),
    dateOfBirth: LocalizedDate.epochMinusMonths(240).toString(),
    defaultGamerTag: Random.email(),
    analyticsId: Random.uuid()
  };
  const interestedCreatorsProps = {
    interestedCreator: initialInterestedCreator,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    pageLabels: {
      informationLabels: {
        messages: {
          email: "Email"
        }
      },
      addContentPageLabels: {},
      breadcrumbPageLabels: {},
      communicationPreferencesPageLabels: {},
      commonPageLabels: {},
      connectAccountsLabels: {}
    } as InformationPageLabels &
      CommonPageLabels &
      BreadcrumbPageLabels &
      AddContentPageLabels &
      CommunicationPreferencesPageLabels &
      ConnectAccountsPageLabels,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    pages: [],
    FLAG_COUNTRIES_BY_TYPE: false
  };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];

  mockMatchMedia();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps
      }
    });
    (useDependency as jest.Mock).mockReturnValue({
      applicationsClient: {},
      errorHandler: jest.fn(),
      metadataClient: {},
      configuration: {
        APPLICATIONS_MFE_BASE_URL: "http://localhost:3003",
        BASE_PATH: "/",
        PROGRAM_CODE: "creator_network",
        SUPPORTED_LOCALES: ["en-us", "en-gb"],
        FLAG_PER_PROGRAM_PROFILE: false
      }
    });

  });

  it("shows remote information component", async () => {
    renderPage(<Information {...interestedCreatorsProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
