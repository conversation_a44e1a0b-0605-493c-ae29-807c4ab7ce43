import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import { anInterestedCreatorApplication } from "__tests__/factories/interestedCreators/InterestedCreatorApplication";
import ApplicationRejected from "pages/interested-creators/application-rejected";
import { useDependency } from "@src/context/DependencyContext";
import { ApplicationRejectedPageLabels } from "@src/contentManagement/ApplicationRejectedPageMapper";
import InterestedCreatorApplicationStatus from "@src/interestedCreators/InterestedCreatorApplicationStatus";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

jest.mock("../../../src/context/DependencyContext");

describe("InterestedCreatorsApplicationRejected", () => {
  const locale = "en-us";
  const analytics = {
    checkedApplicationStatus: jest.fn()
  };
  const pageLabels = {
    applicationRejectedLabels: {
      pageTitle: "Application Rejected",
      title: "Application Not Approved",
      description: "Unfortunately, your application was not approved"
    },
    commonPageLabels: {
      creatorNetwork: "Creator Network",
      close: "Close"
    }
  } as unknown as ApplicationRejectedPageLabels & CommonPageLabels;

  const applicationRejectedProps = {
    locale,
    pageLabels,
    application: anInterestedCreatorApplication() as InterestedCreatorApplicationStatus,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale
    }));
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { FLAG_INTERESTED_CREATOR_CAN_APPLY: false },
      analytics: analytics
    });
  });

  it("shows remote application rejected component", async () => {
    render(<ApplicationRejected {...applicationRejectedProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
