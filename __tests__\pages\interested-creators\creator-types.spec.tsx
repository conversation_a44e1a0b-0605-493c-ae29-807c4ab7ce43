import "reflect-metadata";
import React from "react";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { render, screen } from "@testing-library/react";
import { useDependency } from "@src/context/DependencyContext";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { CreatorTypePageLabels } from "@src/contentManagement/CreatorTypePageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import CreatorTypes from "pages/interested-creators/creator-types";
import { mockMatchMedia } from "../../helpers/window";

jest.mock("../../../src/context/index", () => ({
  ...(jest.requireActual("../../../src/context/index") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../src/context/DependencyContext");

describe("InterestedCreatorsCreatorTypes", () => {
  const initialInterestedCreator = {
    nucleusId: Random.nucleusId(),
    originEmail: Random.email(),
    dateOfBirth: LocalizedDate.epochMinusMonths(240).toString(),
    defaultGamerTag: Random.email(),
    analyticsId: Random.uuid()
  };
  const pageLabels = {
    informationLabels: {},
    breadcrumbPageLabels: {},
    creatorTypePageLabels: {
      labels: {
        youtuber: "Youtuber",
        vlogger: "Vlogger",
        photographer: "Photographer",
        designer_artist: "Designer/Artist",
        blogger: "Blogger",
        live_streamer: "Live Streamer",
        podcaster: "Podcaster",
        cosplayer: "Cosplayer",
        animator: "Animator",
        screenshoter: "Screenshoter",
        lifestyle: "Lifestyle",
        other: "Other"
      },
      messages: {
        creatorTypes: ""
      }
    },
    commonPageLabels: {}
  } as InformationPageLabels & BreadcrumbPageLabels & CreatorTypePageLabels & CommonPageLabels;
  const interestedCreatorsCreatorTypesProps = {
    interestedCreator: initialInterestedCreator,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    pageLabels: pageLabels
  };
  const router = { locale: "en-us", push: jest.fn() };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];

  mockMatchMedia()

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps
      }
    });
    (useDependency as jest.Mock).mockReturnValue({
      applicationsClient: {},
      errorHandler: jest.fn(),
      metadataClient: {},
      configuration: { BASE_PATH: "/support-a-creator" },
      analytics: {}
    });
  });

  it("shows remote creator type component", async () => {
    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
