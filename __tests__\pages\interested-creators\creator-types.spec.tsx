import "reflect-metadata";
import React from "react";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { render, screen } from "@testing-library/react";
import { mockMatchMedia } from "../../helpers/window";
import BrowserAnalytics, { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { CreatorTypePageLabels } from "@src/contentManagement/CreatorTypePageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import CreatorTypes from "pages/interested-creators/creator-types";
import { useDependency } from "@src/context/DependencyContext";
import { anInitialInterestedCreator } from "__tests__/factories/initialInterestedCreators/InitialInterestedCreator";

jest.mock("../../../src/context/index", () => ({
  ...(jest.requireActual("../../../src/context/index") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../src/context/DependencyContext");
jest.mock("next/router");

// Mock the dynamic component with props tracking
const mockCreatorType = jest.fn();
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = (props: any) => {
    mockCreatorType(props);
    return <div data-testid="creator-type-component" {...props} />;
  };
  DynamicComponent.displayName = "CreatorType";
  return DynamicComponent;
});

describe("InterestedCreatorsCreatorTypes", () => {
  const mockDispatch = jest.fn();

  const initialInterestedCreator = {
    ...anInitialInterestedCreator(),
    nucleusId: 12345,
    originEmail: Random.email(),
    dateOfBirth: LocalizedDate.epochMinusMonths(240).toString(),
    defaultGamerTag: Random.email()
  };
  const pageLabels = {
    informationLabels: {},
    breadcrumbPageLabels: {},
    creatorTypePageLabels: {
      labels: {
        youtuber: "Youtuber",
        vlogger: "Vlogger",
        photographer: "Photographer",
        designer_artist: "Designer/Artist",
        blogger: "Blogger",
        live_streamer: "Live Streamer",
        podcaster: "Podcaster",
        cosplayer: "Cosplayer",
        animator: "Animator",
        screenshoter: "Screenshoter",
        lifestyle: "Lifestyle",
        other: "Other"
      },
      messages: {
        creatorTypes: ""
      }
    },
    commonPageLabels: {}
  } as InformationPageLabels & BreadcrumbPageLabels & CreatorTypePageLabels & CommonPageLabels;
  const interestedCreatorsCreatorTypesProps = {
    interestedCreator: initialInterestedCreator,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    analytics: {} as unknown as BrowserAnalytics,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    pageLabels: pageLabels
  };
  const router = { locale: "en-us", push: jest.fn() };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  mockMatchMedia();

  beforeEach(() => {
    jest.clearAllMocks();
    mockCreatorType.mockClear();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: null,
        sessionUser: null,
        isLoading: false
      }
    });
    (useDependency as jest.Mock).mockReturnValue({
      applicationsClient: {},
      errorHandler: jest.fn(),
      metadataClient: {},
      configuration: { BASE_PATH: "" }
    });
  });

  it("shows creator type component", async () => {
    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    expect(await screen.findByTestId("creator-type-component")).toBeInTheDocument();
  });

  it("renders with exception code and shows error page", async () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: 500,
        sessionUser: { id: "test-user" },
        isLoading: false
      }
    });

    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    expect(screen.queryByTestId("creator-type-component")).not.toBeInTheDocument();
  });

  it("passes correct labels to CreatorType component", async () => {
    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    await screen.findByTestId("creator-type-component");

    expect(mockCreatorType).toHaveBeenCalledWith(
      expect.objectContaining({
        labels: expect.objectContaining({
          requiredMessage: "",
          creatorsTypeLabels: expect.arrayContaining([
            expect.objectContaining({
              value: "YOUTUBER",
              label: "Youtuber"
            })
          ])
        })
      })
    );
  });

  it("passes correct interested creator to CreatorType component", async () => {
    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    await screen.findByTestId("creator-type-component");

    expect(mockCreatorType).toHaveBeenCalledWith(
      expect.objectContaining({
        interestedCreator: initialInterestedCreator
      })
    );
  });

  it("passes correct INTERESTED_CREATOR_REAPPLY_PERIOD flag", async () => {
    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    await screen.findByTestId("creator-type-component");

    expect(mockCreatorType).toHaveBeenCalledWith(
      expect.objectContaining({
        INTERESTED_CREATOR_REAPPLY_PERIOD: false
      })
    );
  });

  it("passes correct redirect URL to CreatorType component", async () => {
    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    await screen.findByTestId("creator-type-component");

    expect(mockCreatorType).toHaveBeenCalledWith(
      expect.objectContaining({
        redirectedToNextStepUrl: "/interested-creators/franchises-you-play"
      })
    );
  });

  it("passes analytics to CreatorType component", async () => {
    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    await screen.findByTestId("creator-type-component");

    expect(mockCreatorType).toHaveBeenCalledWith(
      expect.objectContaining({
        analytics: expect.any(Object)
      })
    );
  });

  it("renders with INTERESTED_CREATOR_REAPPLY_PERIOD enabled", async () => {
    const propsWithReapplyEnabled = {
      ...interestedCreatorsCreatorTypesProps,
      INTERESTED_CREATOR_REAPPLY_PERIOD: true
    };

    render(<CreatorTypes {...propsWithReapplyEnabled} />);

    await screen.findByTestId("creator-type-component");

    expect(mockCreatorType).toHaveBeenCalledWith(
      expect.objectContaining({
        INTERESTED_CREATOR_REAPPLY_PERIOD: true
      })
    );
  });
});
