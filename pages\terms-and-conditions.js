import "reflect-metadata";
import { <PERSON><PERSON>, Checkbox, Input, RadioButton, Select } from "@eait-playerexp-cn/core-ui-kit";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useRouter } from "next/router";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import Form from "../components/Form";
import CreatorForm from "../components/FormRules/CreatorForm";
import MigrationLayout from "../components/MigrationLayout";
import Footer from "../components/migrations/Footer";
import { Toast, useToast } from "../components/toast";
import labelsBreadCrumb from "../config/translations/breadcrumb";
import labelsCommon from "../config/translations/common";
import labelsInformation from "../config/translations/information";
import labelsTermsAndConditions from "../config/translations/terms-and-conditions";
import CreatorsService from "../src/api/services/CreatorsService";
import TermsAndConditionsService from "../src/api/services/TermsAndConditionsService";
import { useAppContext } from "../src/context";
import RedirectException from "../src/utils/RedirectException";
import withAuthenticatedUser from "../src/utils/WithAuthenticatedUser";
import withNeedToSignTC from "../src/utils/WithNeedToSignTC";
import {
  DEFAULT_LOCALE,
  ERROR,
  HAS_EXCEPTION,
  isObj,
  isString,
  LOADING,
  onToastClose,
  toastContent,
  useAsync,
  useIsMounted,
  USER_NAVIGATED,
  VALIDATION_ERROR
} from "../utils";
import Error from "./_error";
import BrowserAnalytics, { AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import labelsOpportunities from "../config/translations/opportunities";
import MigrationModal from "../components/migrations/MigrationModal";
import CancelRegistrationModal from "../components/pages/interested-creators/CancelRegistrationModal";
import flags from "../utils/feature-flags";
import { addLocaleCookie, addTelemetryInformation } from "@eait-playerexp-cn/server-kernel";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "../src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import runtimeConfiguration from "../src/configuration/runtimeConfiguration";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import termsAndConditionsProps from "../src/serverprops/TermsAndConditionsProps";
import saveInitialPage from "../src/serverprops/middleware/SaveInitialPage";
import checkTermsAndConditionsUpToDate from "../src/serverprops/middleware/CheckTermsAndConditionsUpToDate";
import errorLogger from "../src/serverprops/middleware/ErrorLogger";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

const TermsAndConditionsForm = ({
  submitTnCHandler,
  termsAndConditionsLabels,
  legalEntity,
  rules,
  mailingAddress,
  countries,
  unRegistered,
  layout,
  onClose,
  isPending,
  navigateToPage,
  setShowMigration,
  showMigration,
  stableDispatch,
  router
}) => {
  const { getValues, formState } = useFormContext();
  const data = getValues();
  const formModified = useMemo(() => !!Object.keys(formState.touchedFields).length || !!formState.isDirty, [formState]);
  const {
    state: { userNavigated },
    dispatch
  } = useAppContext();

  const onSave = () => submitTnCHandler(data, true, navigateToPage);

  /**
   * Discard will be called as follows:
   * - On click of Discard button from confirmation modal, which is shown when creator navigates away using back or previous stepper buttons
   */
  const onDiscard = () => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: false });
    navigateToPage ? router.push(navigateToPage) : router.push("/communication-preferences");
  };

  /**
   * User can navigate with back or using stepper links
   * - navigateToPage handles stepper links
   * - else handle the back navigation
   */
  useEffect(() => {
    if (userNavigated && !formModified) {
      navigateToPage ? router.push(navigateToPage) : router.push("/communication-preferences");
      dispatch && dispatch({ type: USER_NAVIGATED, data: false });
    }
  }, [formModified, router, userNavigated, navigateToPage]);

  return (
    <>
      <PactEntityForm
        {...{
          termsAndConditionsLabels,
          legalEntity,
          rules,
          mailingAddress,
          countries,
          unRegistered
        }}
      />
      <FooterWrapper {...{ layout, onClose, isPending }} />
      <MigrationModal {...{ setShowMigration, showMigration, onSave, onDiscard }} />
    </>
  );
};

const EntityForm = ({ termsAndConditionsLabels }) => {
  const LegalEntityOptions = useMemo(() => {
    return [
      { value: "INDIVIDUAL", label: termsAndConditionsLabels.labels.entityIndividual },
      { value: "BUSINESS", label: termsAndConditionsLabels.labels.entityBusiness }
    ];
  }, [termsAndConditionsLabels.labels.entityBusiness, termsAndConditionsLabels.labels.entityIndividual]);
  const { control } = useFormContext();
  const updateEntityType = useCallback(({ value }, field) => {
    field.onChange(value);
  }, []);

  return (
    <section className="terms-condition-entity">
      <p>{termsAndConditionsLabels.enterDetails}</p>
      <fieldset>
        <legend>{termsAndConditionsLabels.labels.entityType}</legend>
        <aside>
          <Controller
            control={control}
            name="entityType"
            render={({ field, fieldState: { error } }) => (
              <RadioButton
                errorMessage={(error && error.message) || ""}
                {...field}
                selectedOption={{ value: field.value }}
                onChange={(item) => updateEntityType(item, field)}
                options={LegalEntityOptions}
              />
            )}
          />
        </aside>
      </fieldset>
    </section>
  );
};

const PersonalDetailsForm = ({ termsAndConditionsLabels, rules }) => {
  const { control, watch, unregister } = useFormContext();
  const entity = watch("entityType");
  useEffect(() => {
    if (entity === "INDIVIDUAL") {
      unregister("businessName", { keepDefaultValue: true });
    }
  }, [entity, unregister]);

  return (
    <section className="terms-condition-personal">
      {entity === "BUSINESS" && (
        <div className="personal-input-cont business">
          <Controller
            control={control}
            name="businessName"
            rules={rules.businessName}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={(error && error.message) || ""}
                {...field}
                label={termsAndConditionsLabels.labels.businessName}
                placeholder={termsAndConditionsLabels.labels.businessName}
                id="businessName"
                helpText={termsAndConditionsLabels.labels.busnessNameInputDesc}
              />
            )}
          />
        </div>
      )}
      <div className="personal-input-cont">
        <Controller
          control={control}
          name="firstName"
          rules={rules.firstName}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={(error && error.message) || ""}
              {...field}
              label={termsAndConditionsLabels.labels.firstName}
              placeholder={termsAndConditionsLabels.labels.firstName}
            />
          )}
        />
        <Controller
          control={control}
          name="lastName"
          rules={rules.lastName}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={(error && error.message) || ""}
              {...field}
              label={termsAndConditionsLabels.labels.lastName}
              placeholder={termsAndConditionsLabels.labels.lastName}
            />
          )}
        />
      </div>
      <div className="personal-input-cont">
        <Controller
          control={control}
          name="screenName"
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={(error && error.message) || ""}
              {...field}
              label={termsAndConditionsLabels.labels.screenName}
              placeholder={termsAndConditionsLabels.labels.screenName}
              disabled
            />
          )}
        />
        <Controller
          control={control}
          name="email"
          rules={rules.email}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={(error && error.message) || ""}
              {...field}
              label={termsAndConditionsLabels.labels.email}
              placeholder={termsAndConditionsLabels.labels.email}
            />
          )}
        />
      </div>
    </section>
  );
};

const AddressForm = ({ termsAndConditionsLabels, legalEntity, rules, mailingAddress, countries, unRegistered }) => {
  const [asMailingAddress, setAsMailingAddress] = useState(
    unRegistered || checkSameAddress(mailingAddress, legalEntity)
  );
  const { register, control, setValue } = useFormContext();
  const sameAddress = asMailingAddress;
  const {
    city = "",
    country = { value: "", name: "", label: "" },
    state = "",
    street = "",
    zipCode = ""
  } = (sameAddress && mailingAddress) || legalEntity || {};

  const Country = useCallback(
    ({ error, field }) =>
      (sameAddress && (
        <Input
          errorMessage={(error && error.message) || ""}
          {...field}
          label={termsAndConditionsLabels.labels.country}
          placeholder={termsAndConditionsLabels.labels.country}
          disabled={sameAddress}
          id="country"
        />
      )) || (
        <Select
          id="terms-and-conditions"
          selectedOption={country}
          errorMessage={(error && error.message) || ""}
          {...field}
          onChange={(item) => {
            field.onChange(item);
          }}
          label={termsAndConditionsLabels.labels.country}
          options={countries}
          dark
        />
      ),
    [sameAddress, termsAndConditionsLabels.labels.country, countries]
  );
  const sameAddressFld = register("sameAddress");
  const onSameAddressChange = useCallback(
    (e) => {
      setAsMailingAddress(e.target.checked);
      sameAddressFld.onChange(e);
      setValue("sameAddress", e.target.checked);
      if (e.target.checked) {
        const {
          city = "",
          country = { value: "", name: "", label: "" },
          state = "",
          street = "",
          zipCode = ""
        } = mailingAddress;
        setValue("country", (isString(country) && country) || (isObj(country) && country.name));
        setValue("state", state);
        setValue("zipCode", zipCode);
        setValue("city", city);
        setValue("street", street);
      } else {
        const {
          city = "",
          country = { value: "", name: "", label: "" },
          state = "",
          street = "",
          zipCode = ""
        } = legalEntity;
        setValue("country", country);
        setValue("state", state);
        setValue("zipCode", zipCode);
        setValue("city", city);
        setValue("street", street);
      }
    },
    [legalEntity, sameAddressFld, setValue, mailingAddress]
  );

  return (
    <section className="terms-condition-address">
      <div className={`legal-entity-field ${(asMailingAddress && "asMailingAddress") || ""}`}>
        <Controller
          control={control}
          name="sameAddress"
          render={() => (
            <Checkbox
              options={[
                {
                  id: "sameAddress",
                  label: termsAndConditionsLabels.labels.sameAddress,
                  isChecked: asMailingAddress,
                  onChange: onSameAddressChange,
                  dark: true
                }
              ]}
            />
          )}
        />
      </div>
      <div className="address-cont">
        <Controller
          control={control}
          name="street"
          rules={rules.street}
          defaultValue={street}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={(error && error.message) || ""}
              {...field}
              label={termsAndConditionsLabels.labels.street}
              placeholder={termsAndConditionsLabels.labels.street}
              disabled={sameAddress}
              id="street"
            />
          )}
        />
        <Controller
          control={control}
          name="city"
          rules={rules.city}
          defaultValue={city}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={(error && error.message) || ""}
              {...field}
              label={termsAndConditionsLabels.labels.city}
              placeholder={termsAndConditionsLabels.labels.city}
              disabled={sameAddress}
              id="city"
            />
          )}
        />
        <Controller
          control={control}
          key={`country-${!!sameAddress}`}
          name="country"
          rules={rules.country}
          defaultValue={sameAddress ? (isString(country) && country) || (isObj(country) && country.name) : country}
          render={({ field, fieldState: { error } }) => <Country {...{ error, field }} />}
        />
        <Controller
          control={control}
          name="state"
          rules={rules.state}
          defaultValue={state}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={(error && error.message) || ""}
              {...field}
              label={termsAndConditionsLabels.labels.state}
              placeholder={termsAndConditionsLabels.labels.state}
              disabled={sameAddress}
              id="state"
            />
          )}
        />
        <Controller
          control={control}
          name="zipCode"
          rules={rules.zipCode}
          defaultValue={zipCode}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={(error && error.message) || ""}
              {...field}
              label={termsAndConditionsLabels.labels.zip}
              placeholder={termsAndConditionsLabels.labels.zip}
              disabled={sameAddress}
              id="zipCode"
            />
          )}
        />
      </div>
    </section>
  );
};

const FooterWrapper = memo(function FooterWrapper({ layout, onClose, isPending }) {
  const { watch } = useFormContext();
  const entity = watch("entityType");
  return (
    <Footer
      {...{
        buttons: layout.buttons,
        onCancel: onClose,
        disableSubmit: !entity || isPending,
        isPending
      }}
    />
  );
});

const PactEntityForm = memo(function PactEntityForm({
  termsAndConditionsLabels,
  legalEntity = {},
  rules,
  mailingAddress,
  countries,
  unRegistered
}) {
  const entityType = legalEntity?.entityType || "";
  const { watch } = useFormContext();
  const entity = watch("entityType");
  return (
    <div className="pact-safe-entity-form">
      <EntityForm {...{ termsAndConditionsLabels, entityType }} />
      {entity && (
        <div className="personal-info-address-container">
          <PersonalDetailsForm {...{ termsAndConditionsLabels, rules }} />
          {legalEntity && (
            <AddressForm
              {...{ termsAndConditionsLabels, legalEntity, rules, mailingAddress, countries, unRegistered }}
            />
          )}
        </div>
      )}
    </div>
  );
});

const checkSameAddress = (mailingAddress, legalEntity) => {
  if (!legalEntity) {
    return false;
  }
  if (mailingAddress?.state !== legalEntity?.state) {
    return false;
  }
  if (mailingAddress?.city !== legalEntity?.city) {
    return false;
  }
  if (mailingAddress?.street !== legalEntity?.street) {
    return false;
  }
  if (mailingAddress?.zipCode !== legalEntity?.zipCode) {
    return false;
  }
  if (mailingAddress?.country && legalEntity?.country) {
    if (mailingAddress.country.name !== legalEntity.country.name) {
      return false;
    }
  } else {
    return false;
  }
  return true;
};

export default function TermsAndConditions({
  locale,
  user,
  urlLocale,
  opportunityId,
  initialPage,
  analytics = new BrowserAnalytics(user),
  FLAG_COUNTRIES_BY_TYPE
}) {
  const {
    metadataClient,
    creatorsClient,
    errorHandler,
    configuration: { PROGRAM_CODE, FLAG_PER_PROGRAM_PROFILE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const creatorService = useMemo(() => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);
  const router = useRouter();
  const isMounted = useIsMounted();
  const {
    dispatch,
    state: { isLoading, isValidationError, isError, exceptionCode = null, sessionUser = null, userNavigated }
  } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const { t } = useTranslation(["common", "breadcrumb", "information", "opportunities"]);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [creator, setCreator] = useState(false);
  const [countries, setCountries] = useState([]);
  const [pactSafeUrl, setPactSafeUrl] = useState(null);
  const [showPactSafeIframe, setShowPactSafeIframe] = useState(false);
  const [showDeclineModal, setShowDeclineModal] = useState(false);
  const [showDeclineButton, setShowDeclineButton] = useState(true);
  const [showMigration, setShowMigration] = useState(false);
  const [navigateToPage, setNavigateToPage] = useState("");
  const { layout, termsAndConditionsLabels, infoLabels } = useMemo(() => {
    return {
      layout: {
        ...labelsCommon(t),
        ...labelsBreadCrumb(t),
        ...labelsOpportunities(t)
      },
      infoLabels: labelsInformation(t),
      termsAndConditionsLabels: labelsTermsAndConditions(t)
    };
  }, [t]);
  const {
    main: { unhandledError }
  } = layout;
  const onClose = useCallback(() => setShowConfirmation(true), []);
  const handleModalClose = useCallback(() => {
    setShowConfirmation(false);
    setShowDeclineModal(false);
  }, []);

  const handleCancelRegistration = useCallback(() => {
    analytics.canceledOnboardingFlow({ locale: router.locale, page: router.pathname });
    router.push("/api/logout");
  }, [router]);

  /** Form submission is required in following scenarios
   * 1. On click of Next button
   * 2. On click of save button in confirmation modal, when creator tries to navigate away with back or stepper buttons
   */
  const submitTnCHandler = useCallback(
    async (data, navigateBack, navigateToPage) => {
      const {
        businessName = null,
        firstName,
        lastName,
        screenName,
        email,
        country,
        street,
        city,
        state,
        zipCode,
        entityType,
        sameAddress
      } = data;
      const { id: creatorId, mailingAddress } = creator;
      const payload = {
        businessName,
        creatorId,
        locale,
        firstName,
        lastName,
        screenName,
        email,
        country: country.name || country
      };
      let legalCountry = FLAG_PER_PROGRAM_PROFILE ? { code: country.value, name: country.name } : country;
      legalCountry = sameAddress
        ? {
            value: mailingAddress.country.value,
            code: mailingAddress.country.value,
            name: mailingAddress.country.name,
            label: mailingAddress.country.name
          }
        : legalCountry;
      const legalEntity = { street, country: legalCountry, city, state, zipCode, businessName, entityType };
      const legalInformation = sameAddress
        ? { ...mailingAddress, country: legalCountry, businessName, entityType }
        : legalEntity;
      const communicationPreferences = { ...creator.communicationPreferences, email };
      stableDispatch({ type: USER_NAVIGATED, data: false });

      // Creators BFF PUT
      try {
        stableDispatch({ type: LOADING, data: true });
        creator.accountInformation.firstName = data.firstName;
        creator.accountInformation.lastName = data.lastName;
        const accountInformation = {
          ...creator.accountInformation,
          dateOfBirth: LocalizedDate.format(creator.accountInformation.dateOfBirth.toDate(), "YYYY-MM-DD")
        };
        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({
              legalInformation,
              accountInformation,
              communicationPreferences,
              program: { code: PROGRAM_CODE }
            })
          : await CreatorsService.update({ legalInformation, accountInformation, communicationPreferences });

        if (navigateToPage) {
          router.push(navigateToPage);
        } else if (navigateBack) {
          router.push("/communication-preferences");
        } else {
          const result = await TermsAndConditionsService.getSigningUrl({ ...payload, program: PROGRAM_CODE });
          stableDispatch({ type: LOADING, data: false });
          setPactSafeUrl(result.data.contractUrl);
          setShowPactSafeIframe(true);
          stableDispatch({ type: LOADING, data: false });
        }
      } catch (e) {
        stableDispatch({ type: LOADING, data: false });
        errorHandler(stableDispatch, e);
      }
    },
    [creator, locale, stableDispatch]
  );
  const { pending: isPending, execute: submitTnCHandlerClb } = useAsync(submitTnCHandler, false);

  const handleIframeMessage = useCallback(
    async ({ data: { event: userAction } }, creator) => {
      if (userAction === "request_signed" && creator) {
        analytics.signedTermsAndConditions({ locale: router.locale, accepted: true });
        stableDispatch({ type: LOADING, data: true });
        setShowDeclineButton(false);
        let redirectUrl;
        redirectUrl =
          creator.accountInformation.status === "UNREGISTERED"
            ? `${urlLocale}/signup-complete`
            : `${urlLocale}/dashboard`;
        // Creators BFF PUT
        try {
          const updatedAccountInformation = {
            ...creator.accountInformation,
            dateOfBirth: LocalizedDate.format(creator.accountInformation.dateOfBirth.toDate(), "YYYY-MM-DD")
          };
          FLAG_PER_PROGRAM_PROFILE
            ? await creatorService.updateCreator({
                accountInformation: { ...updatedAccountInformation },
                program: { code: PROGRAM_CODE, status: "ACTIVE" }
              })
            : await CreatorsService.update({ accountInformation: { ...updatedAccountInformation, status: "ACTIVE" } });
          await TermsAndConditionsService.clearSignedStatusForTier(router.locale);
          if (opportunityId) {
            if (redirectUrl === `${urlLocale}/signup-complete`) {
              analytics.completedOnboardingFlow({ locale: router.locale });
            }
            redirectUrl = `${urlLocale}/opportunities/${opportunityId}`;
          }
          if (initialPage) {
            redirectUrl = initialPage;
          }
          if (redirectUrl === `${urlLocale}/signup-complete`) {
            analytics.completedOnboardingFlow({ locale: router.locale });
          }
          router.push(redirectUrl);
        } catch (e) {
          errorHandler(stableDispatch, e);
        } finally {
          stableDispatch({ type: LOADING, data: false });
        }
      } else if (userAction === "canceled") {
        analytics.signedTermsAndConditions({ locale: router.locale, accepted: false });
        setShowPactSafeIframe(false);
        location.reload(); //reload
      }
    },
    [router, stableDispatch]
  );

  useEffect(() => {
    async function onMount() {
      try {
        stableDispatch({ type: LOADING, data: true });
        // Creators BFF GET
        if (isMounted()) {
          const creator = FLAG_PER_PROGRAM_PROFILE
            ? await creatorService.getCreator(PROGRAM_CODE)
            : (await CreatorsService.getCreatorWithTier()).data;
          setCreator(creator);
          stableDispatch({ type: LOADING, data: false });
        }
      } catch (e) {
        stableDispatch({ type: LOADING, data: false });
        errorHandler(stableDispatch, e);
      }
    }
    onMount();
    return () => {
      setShowPactSafeIframe(false);
    };
  }, [isMounted, stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  useEffect(() => {
    const countries = FLAG_COUNTRIES_BY_TYPE ? metadataService.getCountriesMatching() : metadataService.getCountries();
    countries
      .then((countries) => {
        if (isMounted()) {
          setCountries(countries);
        }
      })
      .catch((e) => {
        stableDispatch({ type: HAS_EXCEPTION, data: e?.response?.status });
        errorHandler(stableDispatch, e);
      });
  }, [isMounted, stableDispatch]);

  useEffect(() => {
    if (creator) {
      const handler = (e) => handleIframeMessage(e, creator);
      window.addEventListener("message", handler);
      return () => {
        window.removeEventListener("message", handler);
      };
    }
  }, [creator, handleIframeMessage]);

  useEffect(() => {
    if (userNavigated && showPactSafeIframe) {
      router.push("/communication-preferences");
      dispatch && dispatch({ type: USER_NAVIGATED, data: false });
    }
  }, [router, userNavigated]);

  const { confirmationDesc1, confirmationDesc2, modalConfirmationTitle } = infoLabels;
  const {
    title,
    titleUpdatedTermsAndConditions,
    subTitleNewUser,
    subTitleExistingUser,
    subTitleUpdatedTermsAndConditions,
    decline,
    declineModalDescription,
    declineModalHeader
  } = termsAndConditionsLabels;
  let legalEntity = FLAG_PER_PROGRAM_PROFILE ? creator?.legalInformation : creator.legalEntity;
  const userStatus = FLAG_PER_PROGRAM_PROFILE ? creator?.program?.status : creator.accountInformation?.status;
  if (FLAG_PER_PROGRAM_PROFILE && !legalEntity) {
    legalEntity = {
      entityType: "",
      businessName: "",
      street: "",
      country: { value: "", name: "", label: "" },
      city: "",
      state: "",
      zipCode: ""
    };
  }
  const { mailingAddress } = creator;
  const unRegistered = ["UNREGISTERED"].includes(userStatus);
  const inActive = ["INACTIVE"].includes(userStatus);
  const defaultValues = useMemo(() => {
    if (creator) {
      const entityType = legalEntity?.entityType || "";
      const businessName = legalEntity?.businessName || "";
      const {
        accountInformation: { firstName, lastName, defaultGamerTag: screenName },
        communicationPreferences: { email }
      } = creator;
      return {
        sameAddress: unRegistered || checkSameAddress(creator.mailingAddress, creator.legalInformation),
        entityType,
        businessName,
        firstName,
        lastName,
        email,
        screenName
      };
    }
    return creator;
  }, [creator, unRegistered]);
  const rules = useMemo(() => CreatorForm.rules(infoLabels), [infoLabels]);

  const onGoBack = () => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: true });
    setShowMigration(true);
  };

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  const declineTermsAndCondition = () => {
    setShowDeclineModal(true);
  };

  const cancelRegistrationModalLabels = {
    title: modalConfirmationTitle,
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    close: layout.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  const declineTermsAndConditionLabels = {
    title: declineModalHeader,
    yes: layout.buttons.declineTermsAndCondition,
    no: layout.buttons.cancel,
    close: layout.buttons.close,
    confirmationDesc1: declineModalDescription
  };

  return (
    <MigrationLayout
      pageTitle={termsAndConditionsLabels.title}
      {...{
        ...layout,
        onClose,
        showHeader: unRegistered,
        isLoading,
        isRegistrationFlow: true,
        isOnboardingFlow: true,
        stableDispatch,
        onGoBack,
        completed: layout.completed,
        setShowMigration: !showPactSafeIframe ? setShowMigration : undefined,
        setNavigateToPage
      }}
      labels={{
        back: layout.buttons.back,
        title: layout.header.creatorNetwork,
        close: layout.buttons.closeHeader
      }}
    >
      <section className="terms-and-conditions">
        <main>
          <header>
            <h3 className="terms-and-condition-title">
              {userStatus && ((!unRegistered && !inActive && titleUpdatedTermsAndConditions) || title)}
            </h3>
            <p className="terms-and-conditions-description">
              {userStatus &&
                ((unRegistered && subTitleNewUser) ||
                  (inActive && subTitleExistingUser) ||
                  ((!unRegistered || !inActive) && subTitleUpdatedTermsAndConditions))}
            </p>
          </header>
          <section className="pactsafe">
            {(showPactSafeIframe && (
              <div className="pactSafe-iframe-cont">
                {" "}
                <hr />
                <iframe src={`/pactsafe-page?url=${pactSafeUrl}`} /> <hr />
                {showDeclineButton && (
                  <Button variant="tertiary" dark size="md" onClick={declineTermsAndCondition}>
                    {decline}
                  </Button>
                )}
              </div>
            )) ||
              (defaultValues && rules && (
                <Form
                  mode="onChange"
                  onSubmit={submitTnCHandlerClb}
                  key="Pact-Safe"
                  revalidate="onChange"
                  {...{ defaultValues }}
                >
                  <TermsAndConditionsForm
                    {...{
                      submitTnCHandler,
                      termsAndConditionsLabels,
                      legalEntity,
                      rules,
                      mailingAddress,
                      countries,
                      unRegistered,
                      layout,
                      onClose,
                      isPending,
                      navigateToPage,
                      setShowMigration,
                      showMigration,
                      stableDispatch,
                      router
                    }}
                  />
                </Form>
              ))}
          </section>
        </main>
        {showConfirmation && (
          <CancelRegistrationModal
            {...{
              labels: cancelRegistrationModalLabels,
              handleModalClose,
              handleCancelRegistration
            }}
          />
        )}
        {showDeclineModal && (
          <CancelRegistrationModal
            {...{
              labels: declineTermsAndConditionLabels,
              handleModalClose,
              handleCancelRegistration
            }}
          />
        )}
      </section>
    </MigrationLayout>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(saveInitialPage(locale))
      .use(addLocaleCookie(locale))
      .use(checkTermsAndConditionsUpToDate(locale))
      .get(termsAndConditionsProps(locale));

    return await router.run(req, res);
  }

  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  await addTelemetryInformation(req, res, () => Promise.resolve());
  await withNeedToSignTC(req, res, locale);

  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;
  const opportunityId = req.session.opportunityId || "";
  const initialPage = req.session.initialPage || "";
  const urlLocale = `${(locale !== DEFAULT_LOCALE && "/" + locale) || ""}`;
  delete req.session.opportunityId;
  delete req.session.initialPage;
  await req.session.save();

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      opportunityId,
      initialPage,
      locale,
      urlLocale,
      ...(await serverSideTranslations(locale, [
        "common",
        "breadcrumb",
        "information",
        "terms-and-conditions",
        "opportunities"
      ])),
      showInitialMessage: req.session.showInitialMessage || false,
      FLAG_COUNTRIES_BY_TYPE: flags.isCountriesByTypeEnabled()
    }
  };
};
