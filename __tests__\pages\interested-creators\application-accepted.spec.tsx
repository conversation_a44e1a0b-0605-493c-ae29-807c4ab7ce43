import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import ApplicationAccepted from "pages/interested-creators/application-accepted";
import { ApplicationAcceptedPageLabels } from "@src/contentManagement/ApplicationAcceptedPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../../src/context/DependencyContext");

describe("InterestedCreatorsApplicationAccepted", () => {
  const locale = "en-us";
  const analytics = {
    checkedApplicationStatus: jest.fn()
  };
  const pageLabels = {
    applicationAcceptedLabels: {
      pageTitle: "Application Accepted"
    },
    commonPageLabels: {
      creatorNetwork: "Creator Network",
      close: "Close"
    }
  } as unknown as ApplicationAcceptedPageLabels & CommonPageLabels;

  const applicationAcceptedProps = {
    pageLabels,
    locale,
    onButtonClick: () => {}
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale
    }));
    (useDependency as jest.Mock).mockReturnValue({
      analytics: analytics
    });
  });

  it("shows tab title as 'Submission Completed'", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    expect(document.title).toMatch(/Application Accepted/);
  });

  it("shows remote accepted component", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
